# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Documentation
*.md
docs/

# Frontend (if serving separately)
frontend/

# Logs
*.log
logs/

# Temporary files
tmp/
temp/

# Node modules (if any)
node_modules/

# Coverage reports
htmlcov/
.coverage
.coverage.*
coverage.xml
*.cover

# Pytest
.pytest_cache/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json
