# 🐳 CardioMed AI Docker Deployment Guide

This guide explains how to deploy CardioMed AI backend using Docker, including the MCP Toolbox integration.

## 📋 Prerequisites

- **Docker Desktop** installed and running
- **Docker Compose** (included with Docker Desktop)
- **Git** (to clone the repository)

## 🚀 Quick Start

### 1. **Environment Configuration**

Create a `.env` file in the project root:

```env
# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Azure AI Foundry Configuration
AZURE_AI_PROJECT_CONNECTION_STRING=your_azure_ai_project_connection_string_here

# Azure Authentication (Choose one method)
# Method 1: Service Principal (Recommended for production)
AZURE_CLIENT_ID=your_service_principal_client_id
AZURE_CLIENT_SECRET=your_service_principal_secret
AZURE_TENANT_ID=your_azure_tenant_id

# Method 2: Use Azure CLI login (run: docker-compose exec cardiomed-ai az login)

# OCR Configuration (Optional - for Groq)
GROQ_API_KEY=your_groq_api_key_here

# Database Configuration
DATABASE_URL=sqlite:///data/hypertension.db

# Application Settings
DEBUG=false
LOG_LEVEL=info
```

### 2. **Azure Authentication Setup**

#### **Option A: Azure CLI (Recommended)**
```bash
# Login to Azure on your host machine
az login

# Verify login and check subscription
az account show

# The container will automatically use your Azure CLI credentials
```

#### **Option B: Service Principal**
Add to your `.env` file:
```env
AZURE_CLIENT_ID=your_service_principal_client_id
AZURE_CLIENT_SECRET=your_service_principal_secret
AZURE_TENANT_ID=your_azure_tenant_id
```

#### **Option C: Login Inside Container**
```bash
# After container starts, login inside the container
docker-compose exec cardiomed-ai az login
```

### 3. **Build and Deploy**

#### **Development Mode (Backend Only)**
```bash
# Build the Docker image
docker build -t cardiomed-ai:latest .

# Start the backend service
docker-compose up -d cardiomed-ai

# Check status
docker-compose ps
```

#### **Production Mode (Backend + Nginx + Frontend)**
```bash
# Build and start all services
docker-compose --profile production up -d

# Check status
docker-compose ps
```

## 🔧 Docker Architecture

### **Services Included:**

1. **CardioMed AI Backend**
   - FastAPI application
   - MCP Toolbox for database tools
   - OCR processing
   - Azure AI integration

2. **Nginx (Production)**
   - Reverse proxy
   - Static file serving
   - Rate limiting
   - Security headers

### **Key Features:**

- ✅ **MCP Toolbox Integration** - Automatically downloads and configures toolbox v0.6.0
- ✅ **Database Persistence** - SQLite database stored in Docker volume
- ✅ **Health Checks** - Automatic service monitoring
- ✅ **Auto-restart** - Services restart on failure
- ✅ **Environment Variables** - Secure configuration management

## 📁 Directory Structure

```
cardiomed-ai-1.0-dev/
├── Dockerfile                 # Main application container
├── docker-compose.yml         # Service orchestration
├── nginx.conf                 # Nginx configuration
├── .dockerignore              # Docker build exclusions
├── deploy.sh                  # Deployment script (Linux/Mac)
├── data/                      # Persistent database storage
│   └── hypertension.db
├── app/                       # Application code
│   ├── advisor_agent/
│   │   └── tools.yaml         # MCP Toolbox configuration
│   └── ...
└── frontend/                  # Web interface files
```

## 🛠️ Management Commands

### **Basic Operations**

```bash
# Start services
docker-compose up -d

# Stop services
docker-compose down

# View logs
docker-compose logs -f cardiomed-ai

# Restart services
docker-compose restart

# Check service status
docker-compose ps
```

### **Development Commands**

```bash
# Build image only
docker build -t cardiomed-ai:latest .

# Start with logs visible
docker-compose up

# Execute commands in container
docker-compose exec cardiomed-ai bash

# View database
docker-compose exec cardiomed-ai sqlite3 /app/data/hypertension.db
```

### **Maintenance Commands**

```bash
# Update and restart
docker-compose pull
docker-compose up -d

# Clean up unused resources
docker system prune

# Remove all containers and volumes
docker-compose down -v
docker image rm cardiomed-ai:latest
```

## 🌐 Access Points

### **Development Mode**
- **API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/

### **Production Mode**
- **Frontend**: http://localhost
- **API**: http://localhost/api
- **API Docs**: http://localhost/api/docs

## 🔍 Troubleshooting

### **Common Issues**

1. **Port Already in Use**
   ```bash
   # Check what's using port 8000
   netstat -ano | findstr :8000
   
   # Change port in docker-compose.yml
   ports:
     - "8001:8000"  # Use different external port
   ```

2. **Database Issues**
   ```bash
   # Reset database
   docker-compose down -v
   docker-compose up -d
   ```

3. **MCP Toolbox Not Starting**
   ```bash
   # Check logs
   docker-compose logs cardiomed-ai
   
   # Verify tools.yaml configuration
   docker-compose exec cardiomed-ai cat /app/app/advisor_agent/tools.yaml
   ```

4. **Environment Variables Not Loading**
   ```bash
   # Verify .env file exists and has correct format
   # Restart containers after .env changes
   docker-compose down
   docker-compose up -d
   ```

### **Health Checks**

```bash
# Check if backend is healthy
curl http://localhost:8000/

# Check database connection
curl http://localhost:8000/users/

# Check MCP Toolbox
docker-compose exec cardiomed-ai ps aux | grep toolbox
```

## 📊 Monitoring

### **View Logs**
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f cardiomed-ai

# Last 100 lines
docker-compose logs --tail=100 cardiomed-ai
```

### **Resource Usage**
```bash
# Container stats
docker stats

# Disk usage
docker system df
```

## 🔒 Security Considerations

1. **Environment Variables**: Never commit `.env` file to version control
2. **Database**: Stored in Docker volume for persistence
3. **Network**: Services communicate through internal Docker network
4. **Nginx**: Includes security headers and rate limiting
5. **Updates**: Regularly update base images and dependencies

## 🚀 Production Deployment

For production deployment:

1. **Use HTTPS**: Configure SSL certificates in Nginx
2. **Environment**: Set `DEBUG=false` in `.env`
3. **Monitoring**: Add logging and monitoring solutions
4. **Backup**: Regular database backups
5. **Updates**: Implement rolling updates strategy

## 📞 Support

If you encounter issues:

1. Check the logs: `docker-compose logs -f`
2. Verify configuration: `.env` file and `tools.yaml`
3. Test health endpoints
4. Review this documentation

---

**CardioMed AI is now containerized and ready for deployment! 🎉**
