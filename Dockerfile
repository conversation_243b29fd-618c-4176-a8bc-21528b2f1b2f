# Use Python 3.11 slim image as base
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# Set work directory
WORKDIR /app

# Install system dependencies including Azure CLI
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    sqlite3 \
    ca-certificates \
    lsb-release \
    gnupg \
    && curl -sL https://aka.ms/InstallAzureCLIDeb | bash \
    && rm -rf /var/lib/apt/lists/*

# Install uv for faster Python package management
RUN pip install uv

# Copy dependency files
COPY pyproject.toml uv.lock ./

# Install Python dependencies using uv
RUN uv sync --frozen

# Install additional Azure packages that might not be in pyproject.toml
RUN uv pip install azure-ai-projects azure-identity

# Download and setup MCP Toolbox
ENV VERSION=0.6.0
RUN curl -L -o toolbox https://github.com/modelcontextprotocol/toolbox/releases/download/v${VERSION}/toolbox-linux-amd64 \
    && chmod +x toolbox \
    && mv toolbox /usr/local/bin/

# Copy application code
COPY app/ ./app/
COPY migrate_db.py ./

# Create directory for database and tools
RUN mkdir -p /app/data

# Copy and update tools.yaml for container environment
COPY app/advisor_agent/tools.yaml ./app/advisor_agent/tools.yaml.template
RUN sed 's|D:/global health studio/cardiomed-ai-1.0-dev/hypertension.db|/app/data/hypertension.db|g' \
    ./app/advisor_agent/tools.yaml.template > ./app/advisor_agent/tools.yaml

# Database will be initialized by startup script if needed

# Create startup script
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
echo "Starting CardioMed AI Backend..."\n\
echo "Database will be auto-created by FastAPI on first request"\n\
\n\
# Check Azure authentication\n\
echo "Checking Azure authentication..."\n\
if [ -n "$AZURE_CLIENT_ID" ] && [ -n "$AZURE_CLIENT_SECRET" ] && [ -n "$AZURE_TENANT_ID" ]; then\n\
    echo "✅ Using service principal authentication"\n\
    export AZURE_CLIENT_ID=$AZURE_CLIENT_ID\n\
    export AZURE_CLIENT_SECRET=$AZURE_CLIENT_SECRET\n\
    export AZURE_TENANT_ID=$AZURE_TENANT_ID\n\
elif [ -f /root/.azure/accessTokens.json ] || [ -f /root/.azure/azureProfile.json ]; then\n\
    echo "✅ Using Azure CLI authentication (mounted from host)"\n\
    # Test Azure CLI authentication\n\
    if az account show >/dev/null 2>&1; then\n\
        echo "✅ Azure CLI authentication verified"\n\
        AZURE_USER=$(az account show --query user.name -o tsv 2>/dev/null || echo "Unknown")\n\
        echo "   Logged in as: $AZURE_USER"\n\
    else\n\
        echo "⚠️  Azure CLI credentials found but may be expired"\n\
        echo "   Run: docker-compose exec cardiomed-ai az login"\n\
    fi\n\
else\n\
    echo "⚠️  No Azure authentication found. Some features may not work."\n\
    echo "   Option 1: Set environment variables (AZURE_CLIENT_ID, AZURE_CLIENT_SECRET, AZURE_TENANT_ID)"\n\
    echo "   Option 2: Run: az login (on host), then restart container"\n\
    echo "   Option 3: Run: docker-compose exec cardiomed-ai az login"\n\
fi\n\
\n\
# Start MCP Toolbox in background\n\
echo "Starting MCP Toolbox..."\n\
cd /app/app/advisor_agent\n\
toolbox --tools-file tools.yaml &\n\
TOOLBOX_PID=$!\n\
\n\
# Wait for toolbox to start\n\
sleep 5\n\
\n\
# Start FastAPI application\n\
echo "Starting FastAPI server..."\n\
cd /app\n\
uv run uvicorn app.main:app --host 0.0.0.0 --port 8000 &\n\
FASTAPI_PID=$!\n\
\n\
# Function to handle shutdown\n\
shutdown() {\n\
    echo "Shutting down services..."\n\
    kill $TOOLBOX_PID $FASTAPI_PID 2>/dev/null || true\n\
    wait\n\
    exit 0\n\
}\n\
\n\
# Set up signal handlers\n\
trap shutdown SIGTERM SIGINT\n\
\n\
# Wait for processes\n\
wait $FASTAPI_PID\n\
' > /app/start.sh && chmod +x /app/start.sh

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/ || exit 1

# Volume for persistent data
VOLUME ["/app/data"]

# Start the application
CMD ["/app/start.sh"]
