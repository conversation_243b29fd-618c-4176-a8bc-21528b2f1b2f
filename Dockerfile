# Use Python 3.11 slim image as base
FROM python:3.11-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# Set work directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    sqlite3 \
    && rm -rf /var/lib/apt/lists/*

# Install uv for faster Python package management
RUN pip install uv

# Copy dependency files
COPY pyproject.toml uv.lock ./

# Install Python dependencies using uv
RUN uv sync --frozen

# Download and setup MCP Toolbox
ENV VERSION=0.6.0
RUN curl -L -o toolbox https://github.com/modelcontextprotocol/toolbox/releases/download/v${VERSION}/toolbox-linux-amd64 \
    && chmod +x toolbox \
    && mv toolbox /usr/local/bin/

# Copy application code
COPY app/ ./app/
COPY migrate_db.py ./

# Create directory for database and tools
RUN mkdir -p /app/data

# Copy and update tools.yaml for container environment
COPY app/advisor_agent/tools.yaml ./app/advisor_agent/tools.yaml.template
RUN sed 's|D:/global health studio/cardiomed-ai-1.0-dev/hypertension.db|/app/data/hypertension.db|g' \
    ./app/advisor_agent/tools.yaml.template > ./app/advisor_agent/tools.yaml

# Database will be initialized by startup script if needed

# Create startup script
RUN echo '#!/bin/bash\n\
set -e\n\
\n\
# Initialize database if it doesn'\''t exist\n\
if [ ! -f /app/data/hypertension.db ]; then\n\
    echo "Initializing database..."\n\
    cd /app\n\
    uv run python migrate_db.py\n\
    mv hypertension.db /app/data/\n\
fi\n\
\n\
# Start MCP Toolbox in background\n\
echo "Starting MCP Toolbox..."\n\
cd /app/app/advisor_agent\n\
toolbox --tools-file tools.yaml &\n\
TOOLBOX_PID=$!\n\
\n\
# Wait for toolbox to start\n\
sleep 5\n\
\n\
# Start FastAPI application\n\
echo "Starting CardioMed AI Backend..."\n\
cd /app\n\
uv run uvicorn app.main:app --host 0.0.0.0 --port 8000 &\n\
FASTAPI_PID=$!\n\
\n\
# Function to handle shutdown\n\
shutdown() {\n\
    echo "Shutting down services..."\n\
    kill $TOOLBOX_PID $FASTAPI_PID 2>/dev/null || true\n\
    wait\n\
    exit 0\n\
}\n\
\n\
# Set up signal handlers\n\
trap shutdown SIGTERM SIGINT\n\
\n\
# Wait for processes\n\
wait $FASTAPI_PID\n\
' > /app/start.sh && chmod +x /app/start.sh

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/ || exit 1

# Volume for persistent data
VOLUME ["/app/data"]

# Start the application
CMD ["/app/start.sh"]
