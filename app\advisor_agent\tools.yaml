sources:
  my-sqlite-db:
    kind: "sqlite"
    database: "D:/global health studio/cardiomed-ai-1.0-dev/hypertension.db"

tools:
  get_user_profile:
    kind: "sqlite-sql"
    source: "my-sqlite-db"
    description: "Get the user's health profile including target BP, medical conditions, and medications"
    statement: "SELECT * FROM users WHERE id = 1"

  get_bp_history:
    kind: "sqlite-sql"
    source: "my-sqlite-db"
    description: "Get user's blood pressure history with notes, ordered by date"
    statement: "SELECT systolic, diastolic, pulse, reading_time, notes FROM blood_pressure_readings WHERE user_id = 1 ORDER BY reading_time DESC LIMIT 20"

toolsets:
    my_toolset:
        - get_user_profile
        - get_bp_history
