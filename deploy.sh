#!/bin/bash

# CardioMed AI Backend Deployment Script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
}

# Create necessary directories
setup_directories() {
    log_info "Setting up directories..."
    mkdir -p data
    mkdir -p logs
}

# Check if .env file exists
check_env() {
    if [ ! -f .env ]; then
        log_warning ".env file not found. Creating template..."
        cat > .env << EOF
# Azure OpenAI Configuration
AZURE_OPENAI_API_KEY=your_azure_openai_api_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Azure AI Foundry Configuration  
AZURE_AI_PROJECT_CONNECTION_STRING=your_azure_ai_project_connection_string_here

# OCR Configuration (Optional - for Groq)
GROQ_API_KEY=your_groq_api_key_here

# Database Configuration
DATABASE_URL=sqlite:///data/hypertension.db

# Application Settings
DEBUG=false
LOG_LEVEL=info
EOF
        log_warning "Please edit .env file with your actual configuration values"
        return 1
    fi
    return 0
}

# Build Docker image
build_image() {
    log_info "Building CardioMed AI Docker image..."
    docker build -t cardiomed-ai:latest .
    log_success "Docker image built successfully"
}

# Start services
start_services() {
    local profile=${1:-""}
    
    log_info "Starting CardioMed AI services..."
    
    if [ "$profile" = "production" ]; then
        docker-compose --profile production up -d
        log_success "CardioMed AI started in production mode"
        log_info "Frontend available at: http://localhost"
        log_info "API available at: http://localhost/api"
    else
        docker-compose up -d cardiomed-ai
        log_success "CardioMed AI backend started in development mode"
        log_info "API available at: http://localhost:8000"
        log_info "API docs available at: http://localhost:8000/docs"
    fi
}

# Stop services
stop_services() {
    log_info "Stopping CardioMed AI services..."
    docker-compose down
    log_success "Services stopped"
}

# Show logs
show_logs() {
    docker-compose logs -f cardiomed-ai
}

# Show status
show_status() {
    log_info "CardioMed AI Service Status:"
    docker-compose ps
    
    log_info "Health Check:"
    if curl -f http://localhost:8000/ &> /dev/null; then
        log_success "Backend is healthy"
    else
        log_error "Backend is not responding"
    fi
}

# Clean up
cleanup() {
    log_info "Cleaning up Docker resources..."
    docker-compose down -v
    docker image rm cardiomed-ai:latest 2>/dev/null || true
    log_success "Cleanup completed"
}

# Main script
main() {
    case "$1" in
        "build")
            check_docker
            setup_directories
            build_image
            ;;
        "start")
            check_docker
            setup_directories
            if ! check_env; then
                log_error "Please configure .env file first"
                exit 1
            fi
            start_services "$2"
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            start_services "$2"
            ;;
        "logs")
            show_logs
            ;;
        "status")
            show_status
            ;;
        "cleanup")
            cleanup
            ;;
        "deploy")
            check_docker
            setup_directories
            if ! check_env; then
                log_error "Please configure .env file first"
                exit 1
            fi
            build_image
            start_services "$2"
            ;;
        *)
            echo "CardioMed AI Backend Deployment Script"
            echo ""
            echo "Usage: $0 {build|start|stop|restart|logs|status|cleanup|deploy} [production]"
            echo ""
            echo "Commands:"
            echo "  build     - Build Docker image"
            echo "  start     - Start services (add 'production' for full stack)"
            echo "  stop      - Stop services"
            echo "  restart   - Restart services"
            echo "  logs      - Show service logs"
            echo "  status    - Show service status"
            echo "  cleanup   - Remove all containers and images"
            echo "  deploy    - Build and start (full deployment)"
            echo ""
            echo "Examples:"
            echo "  $0 deploy              # Development deployment"
            echo "  $0 deploy production   # Production deployment with Nginx"
            echo "  $0 start               # Start backend only"
            echo "  $0 logs                # View logs"
            exit 1
            ;;
    esac
}

main "$@"
