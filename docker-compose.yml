services:
  cardiomed-ai:
    build: .
    container_name: cardiomed-ai-backend
    ports:
      - "8000:8000"
    volumes:
      - ./data:/app/data
      - ./.env:/app/.env:ro
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=sqlite:///data/hypertension.db
      # Azure Authentication (optional - can use service principal or Azure CLI)
      - AZURE_CLIENT_ID=${AZURE_CLIENT_ID:-}
      - AZURE_CLIENT_SECRET=${AZURE_CLIENT_SECRET:-}
      - AZURE_TENANT_ID=${AZURE_TENANT_ID:-}
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Add a reverse proxy for production
  nginx:
    image: nginx:alpine
    container_name: cardiomed-ai-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./frontend:/usr/share/nginx/html:ro
    depends_on:
      - cardiomed-ai
    restart: unless-stopped
    profiles:
      - production

volumes:
  cardiomed_data:
    driver: local
