/* Add Reading Page Specific Styles */

.add-reading-content {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.back-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 25px;
    color: #2c3e50;
    cursor: pointer;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.back-button:hover {
    background: #e9ecef;
    transform: translateX(-5px);
}

.form-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 20px;
}

.form-header {
    margin-bottom: 30px;
}

.form-header h2 {
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.form-header h2 i {
    color: #667eea;
}

/* Form Styles */
.bp-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    color: #2c3e50;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-group label i {
    color: #667eea;
}

.form-group input,
.form-group textarea {
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: #667eea;
    outline: none;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.input-hint {
    font-size: 0.85rem;
    color: #666;
    margin-top: 4px;
}

.required {
    color: #e74c3c;
    margin-left: 4px;
}

/* Reading Status */
.reading-status {
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    display: none;
}

.reading-status.normal {
    background: #d5f5e3;
    color: #27ae60;
}

.reading-status.elevated {
    background: #fef9e7;
    color: #f39c12;
}

.reading-status.high {
    background: #fadbd8;
    color: #e74c3c;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.form-actions button {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.cancel-btn {
    background: #95a5a6;
    color: white;
}

.cancel-btn:hover {
    background: #7f8c8d;
}

.save-btn {
    background: #667eea;
    color: white;
}

.save-btn:hover {
    background: #5a6fe4;
    transform: translateY(-2px);
}

.save-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
}

/* Input Validation Styles */
.form-group input:invalid {
    border-color: #e74c3c;
}

.form-group input:invalid + .input-hint {
    color: #e74c3c;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-card {
        padding: 20px;
    }

    .form-actions {
        flex-direction: column-reverse;
    }

    .form-actions button {
        width: 100%;
        justify-content: center;
    }
}

/* Loading State */
.save-btn.loading {
    position: relative;
    pointer-events: none;
}

.save-btn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
