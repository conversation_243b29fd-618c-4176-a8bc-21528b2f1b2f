<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add Blood Pressure Reading - CardioMed AI</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="add-reading.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <i class="fas fa-heartbeat"></i>
                <h1>CardioMed AI</h1>
            </div>
            <div class="user-info" onclick="window.location.href='profile.html'" style="cursor: pointer;">
                <span id="user-name">Welcome, Kofi</span>
                <i class="fas fa-user-circle"></i>
            </div>
        </header>

        <!-- Main Content -->
        <main class="add-reading-content">
            <div class="back-button" onclick="window.location.href='index.html'">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </div>

            <div class="form-card">
                <div class="form-header">
                    <h2><i class="fas fa-plus-circle"></i> Add Blood Pressure Reading</h2>
                </div>

                <form id="bpForm" class="bp-form">
                    <div class="form-group">
                        <label for="systolic">
                            <i class="fas fa-arrow-up"></i> Systolic Pressure (mmHg)
                            <span class="required">*</span>
                        </label>
                        <input type="number" id="systolic" name="systolic" required
                               min="70" max="250" placeholder="Enter systolic pressure">
                        <div class="input-hint">Normal range: 90-140 mmHg</div>
                    </div>

                    <div class="form-group">
                        <label for="diastolic">
                            <i class="fas fa-arrow-down"></i> Diastolic Pressure (mmHg)
                            <span class="required">*</span>
                        </label>
                        <input type="number" id="diastolic" name="diastolic" required
                               min="40" max="150" placeholder="Enter diastolic pressure">
                        <div class="input-hint">Normal range: 60-90 mmHg</div>
                    </div>

                    <div class="form-group">
                        <label for="pulse">
                            <i class="fas fa-heartbeat"></i> Pulse Rate (BPM)
                            <span class="required">*</span>
                        </label>
                        <input type="number" id="pulse" name="pulse" required
                               min="30" max="220" placeholder="Enter pulse rate">
                        <div class="input-hint">Normal range: 60-100 BPM</div>
                    </div>

                    <div class="form-group">
                        <label for="device">
                            <i class="fas fa-digital-tachograph"></i> Device Used
                        </label>
                        <input type="text" id="device" name="device"
                               placeholder="Enter device name/model (optional)">
                    </div>

                    <div class="form-group">
                        <label for="notes">
                            <i class="fas fa-sticky-note"></i> Notes
                        </label>
                        <textarea id="notes" name="notes" rows="3"
                                  placeholder="Add any notes about this reading (optional)"></textarea>
                    </div>

                    <div class="reading-status" id="readingStatus">
                        <!-- Status will be shown here after input -->
                    </div>

                    <div class="form-actions">
                        <button type="button" onclick="window.location.href='index.html'" class="cancel-btn">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                        <button type="submit" class="save-btn">
                            <i class="fas fa-save"></i> Save Reading
                        </button>
                    </div>
                </form>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 CardioMed AI - Your Personal Hypertension Management Companion</p>
        </footer>
    </div>

    <script src="add-reading.js"></script>
</body>
</html>
