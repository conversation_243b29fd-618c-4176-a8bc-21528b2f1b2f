/* Chat-specific styles */
.chat-main {
    height: calc(100vh - 140px);
    display: flex;
    justify-content: center;
    align-items: center;
}

.chat-container {
    width: 100%;
    max-width: 900px;
    height: 85vh;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Chat Header */
.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 2px solid #f8f9fa;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.agent-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.agent-avatar {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

.agent-details h2 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.agent-details p {
    margin: 5px 0;
    opacity: 0.9;
    font-size: 0.9rem;
}

.agent-status {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 0.8rem;
}

.agent-status.online i {
    color: #2ecc71;
    animation: pulse 2s infinite;
}

.chat-actions {
    display: flex;
    gap: 10px;
}

.action-btn {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.action-btn.active {
    background: rgba(255, 255, 255, 0.4);
}

/* Navigation */
.nav-actions {
    display: flex;
    align-items: center;
    gap: 20px;
}

.nav-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.nav-btn:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.message {
    display: flex;
    gap: 12px;
    max-width: 80%;
    animation: fadeInUp 0.3s ease;
}

.message.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.agent-message .message-avatar {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.user-message .message-avatar {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    color: white;
}

.message-content {
    flex: 1;
}

.message-text {
    background: #f8f9fa;
    padding: 15px 18px;
    border-radius: 18px;
    line-height: 1.6;
    color: #2c3e50;
}

.user-message .message-text {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.message-text ul {
    margin: 10px 0;
    padding-left: 20px;
}

.message-text li {
    margin: 5px 0;
}

.message-time {
    font-size: 0.75rem;
    color: #95a5a6;
    margin-top: 5px;
    text-align: right;
}

.user-message .message-time {
    text-align: left;
}

.welcome-message .message-text {
    background: linear-gradient(135deg, #e8f4fd, #f0f8ff);
    border: 2px solid #667eea;
}

/* Quick Questions */
.quick-questions {
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    padding: 15px 20px;
    transition: all 0.3s ease;
}

.quick-questions.collapsed .questions-grid {
    display: none;
}

.quick-questions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.quick-questions-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1rem;
}

.quick-questions-header button {
    background: none;
    border: none;
    color: #667eea;
    cursor: pointer;
    font-size: 1rem;
    transition: transform 0.3s ease;
}

.quick-questions.collapsed .quick-questions-header button i {
    transform: rotate(180deg);
}

.questions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.question-btn {
    background: white;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: left;
    font-size: 0.9rem;
    color: #2c3e50;
}

.question-btn:hover {
    border-color: #667eea;
    background: #f0f4ff;
    transform: translateY(-2px);
}

/* Chat Input */
.chat-input-container {
    border-top: 1px solid #e9ecef;
    background: white;
}

.input-options {
    padding: 10px 20px;
    border-bottom: 1px solid #f1f3f4;
}

.context-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
}

.context-toggle input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: #667eea;
}

.context-toggle label {
    font-size: 0.9rem;
    color: #555;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 5px;
}

.chat-input {
    padding: 15px 20px;
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 10px;
    background: #f8f9fa;
    border-radius: 25px;
    padding: 8px 15px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.input-wrapper:focus-within {
    border-color: #667eea;
    background: white;
}

.input-wrapper textarea {
    flex: 1;
    border: none;
    background: none;
    resize: none;
    outline: none;
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.4;
    max-height: 120px;
    min-height: 24px;
}

.send-btn {
    background: #667eea;
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover:not(:disabled) {
    background: #5a6fd8;
    transform: scale(1.1);
}

.send-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
}

.input-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
}

.typing-indicator {
    display: none;
    align-items: center;
    gap: 8px;
    color: #667eea;
    font-size: 0.9rem;
}

.typing-indicator.show {
    display: flex;
}

.typing-dots {
    display: flex;
    gap: 3px;
}

.typing-dots span {
    width: 4px;
    height: 4px;
    background: #667eea;
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

.input-hint {
    font-size: 0.8rem;
    color: #95a5a6;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes typing {
    0%, 60%, 100% { transform: translateY(0); }
    30% { transform: translateY(-10px); }
}

/* Scrollbar Styling */
.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .chat-container {
        height: 90vh;
        margin: 10px;
        border-radius: 15px;
    }
    
    .chat-header {
        padding: 15px 20px;
    }
    
    .agent-details h2 {
        font-size: 1.1rem;
    }
    
    .message {
        max-width: 90%;
    }
    
    .questions-grid {
        grid-template-columns: 1fr;
    }
    
    .nav-btn span {
        display: none;
    }
    
    .input-hint {
        display: none;
    }
}
