<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Blood Pressure History - CardioMed AI</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="history.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns"></script>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <i class="fas fa-heartbeat"></i>
                <h1>CardioMed AI</h1>
            </div>
            <div class="user-info" onclick="window.location.href='profile.html'" style="cursor: pointer;">
                <span id="user-name">Welcome, Kofi</span>
                <i class="fas fa-user-circle"></i>
            </div>
        </header>

        <!-- Main Content -->
        <main class="history-content">
            <div class="back-button" onclick="window.location.href='index.html'">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </div>

            <div class="history-header">
                <h2><i class="fas fa-history"></i> Blood Pressure History</h2>
                <div class="history-filters">
                    <div class="search-box">
                        <i class="fas fa-search"></i>
                        <input type="text" id="searchInput" placeholder="Search notes, device...">
                    </div>
                    <div class="export-controls">
                        <button type="button" class="export-btn" onclick="exportData('csv')">
                            <i class="fas fa-download"></i>
                            <span>CSV</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Graph Section -->
            <div class="card graph-card">
                <div class="card-header">
                    <h3><i class="fas fa-chart-line"></i> Blood Pressure Trend</h3>
                    <div class="graph-controls">
                        <select id="timeRange">
                            <option value="7">Last 7 Days</option>
                            <option value="30">Last 30 Days</option>
                            <option value="90">Last 3 Months</option>
                            <option value="all">All Time</option>
                        </select>
                    </div>
                </div>
                <div class="graph-container">
                    <canvas id="bpTrendGraph"></canvas>
                </div>
            </div>

            <!-- Statistics Summary -->
            <div class="stats-summary">
                <div class="stat-card">
                    <h4>Total Readings</h4>
                    <div class="stat-value" id="totalReadings">-</div>
                </div>
                <div class="stat-card">
                    <h4>Average Systolic</h4>
                    <div class="stat-value" id="avgSystolic">-</div>
                </div>
                <div class="stat-card">
                    <h4>Average Diastolic</h4>
                    <div class="stat-value" id="avgDiastolic">-</div>
                </div>
                <div class="stat-card">
                    <h4>Average Pulse</h4>
                    <div class="stat-value" id="avgPulse">-</div>
                </div>
            </div>

            <!-- Readings Table -->
            <div class="card table-card">
                <div class="table-responsive">
                    <table class="readings-table">
                        <thead>
                            <tr>
                                <th data-sort="date">Date/Time <i class="fas fa-sort"></i></th>
                                <th data-sort="systolic">Systolic <i class="fas fa-sort"></i></th>
                                <th data-sort="diastolic">Diastolic <i class="fas fa-sort"></i></th>
                                <th data-sort="pulse">Pulse <i class="fas fa-sort"></i></th>
                                <th data-sort="status">Status <i class="fas fa-sort"></i></th>
                                <th data-sort="device">Device <i class="fas fa-sort"></i></th>
                                <th>Notes</th>
                            </tr>
                        </thead>
                        <tbody id="readingsTableBody">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 CardioMed AI - Your Personal Hypertension Management Companion</p>
        </footer>
    </div>

    <script src="history.js" type="text/javascript" defer></script>
</body>
</html>
