/* Profile Page Specific Styles */

.profile-content {
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.back-button {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 25px;
    color: #2c3e50;
    cursor: pointer;
    margin-bottom: 20px;
    transition: all 0.3s ease;
}

.back-button:hover {
    background: #e9ecef;
    transform: translateX(-5px);
}

.profile-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 30px;
    margin-bottom: 20px;
}

.profile-header {
    margin-bottom: 30px;
}

.profile-header h2 {
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0;
}

.profile-header h2 i {
    color: #667eea;
}

/* Form Sections */
.form-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.form-section:last-child {
    border-bottom: none;
}

.form-section h3 {
    color: #2c3e50;
    font-size: 1.2rem;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.form-section h3 i {
    color: #667eea;
}

/* Form Groups */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #2c3e50;
    font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    border-color: #667eea;
    outline: none;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.required {
    color: #e74c3c;
    margin-left: 4px;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.form-actions button {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.cancel-btn {
    background: #95a5a6;
    color: white;
}

.cancel-btn:hover {
    background: #7f8c8d;
}

.save-btn {
    background: #667eea;
    color: white;
}

.save-btn:hover {
    background: #5a6fe4;
    transform: translateY(-2px);
}

.save-btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
}

/* Loading State */
.save-btn.loading {
    position: relative;
    pointer-events: none;
}

.save-btn.loading i {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success Message */
.success-message {
    background: #d5f5e3;
    color: #27ae60;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    animation: fadeIn 0.5s ease;
}

/* Error Message */
.error-message {
    background: #fadbd8;
    color: #e74c3c;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .profile-card {
        padding: 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }

    .form-actions {
        flex-direction: column-reverse;
    }

    .form-actions button {
        width: 100%;
        justify-content: center;
    }
}
