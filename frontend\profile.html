<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Edit Profile - CardioMed AI</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="profile.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <i class="fas fa-heartbeat"></i>
                <h1>CardioMed AI</h1>
            </div>
            <div class="user-info">
                <span id="user-name">Welcome, Kofi</span>
                <i class="fas fa-user-circle"></i>
            </div>
        </header>

        <!-- Main Content -->
        <main class="profile-content">
            <div class="back-button" onclick="window.location.href='index.html'">
                <i class="fas fa-arrow-left"></i> Back to Dashboard
            </div>

            <div class="profile-card">
                <div class="profile-header">
                    <h2><i class="fas fa-user-edit"></i> Edit Profile</h2>
                </div>

                <form id="profileForm" class="profile-form">
                    <!-- Personal Information Section -->
                    <div class="form-section">
                        <h3><i class="fas fa-user"></i> Personal Information</h3>
                        
                        <div class="form-group">
                            <label for="fullName">
                                Full Name <span class="required">*</span>
                            </label>
                            <input type="text" id="fullName" name="full_name" required>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="age">
                                    Age <span class="required">*</span>
                                </label>
                                <input type="number" id="age" name="age" min="1" max="120" required>
                            </div>

                            <div class="form-group">
                                <label for="gender">
                                    Gender <span class="required">*</span>
                                </label>
                                <select id="gender" name="gender" required>
                                    <option value="">Select gender</option>
                                    <option value="Male">Male</option>
                                    <option value="Female">Female</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="height">
                                    Height (cm) <span class="required">*</span>
                                </label>
                                <input type="number" id="height" name="height" min="50" max="250" step="0.1" required>
                            </div>

                            <div class="form-group">
                                <label for="weight">
                                    Weight (kg) <span class="required">*</span>
                                </label>
                                <input type="number" id="weight" name="weight" min="20" max="500" step="0.1" required>
                            </div>
                        </div>
                    </div>

                    <!-- Medical Information Section -->
                    <div class="form-section">
                        <h3><i class="fas fa-notes-medical"></i> Medical Information</h3>
                        
                        <div class="form-group">
                            <label for="medicalConditions">Medical Conditions</label>
                            <textarea id="medicalConditions" name="medical_conditions" rows="3"
                                    placeholder="List any medical conditions (e.g., diabetes, heart disease)"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="medications">Current Medications</label>
                            <textarea id="medications" name="medications" rows="3"
                                    placeholder="List your current medications and dosages"></textarea>
                        </div>
                    </div>

                    <div class="form-actions">
                        <button type="button" onclick="window.location.href='index.html'" class="cancel-btn">
                            <i class="fas fa-times"></i> Cancel
                        </button>
                        <button type="submit" class="save-btn">
                            <i class="fas fa-save"></i> Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </main>

        <!-- Footer -->
        <footer class="footer">
            <p>&copy; 2024 CardioMed AI - Your Personal Hypertension Management Companion</p>
        </footer>
    </div>

    <script src="profile.js"></script>
</body>
</html>
