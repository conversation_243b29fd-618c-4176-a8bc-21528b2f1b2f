/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 20px 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 2.5rem;
    color: #e74c3c;
    animation: heartbeat 2s infinite;
}

.logo h1 {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(45deg, #e74c3c, #c0392b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 10px;
    font-weight: 600;
    color: #555;
}

.user-info i {
    font-size: 1.5rem;
    color: #667eea;
}

/* Dashboard Grid */
.dashboard {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

/* Card Styles */
.card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 25px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f8f9fa;
}

.card-header h2 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c3e50;
    display: flex;
    align-items: center;
    gap: 10px;
}

.card-header i {
    color: #667eea;
}

/* BP Reading Card */
.bp-card {
    grid-column: span 2;
}

.bp-reading {
    text-align: center;
}

.bp-numbers {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 15px;
    color: #2c3e50;
}

.systolic {
    color: #e74c3c;
}

.diastolic {
    color: #3498db;
}

.separator {
    color: #95a5a6;
    margin: 0 10px;
}

.unit {
    font-size: 1.2rem;
    color: #7f8c8d;
    margin-left: 10px;
}

.bp-status {
    font-size: 1.2rem;
    font-weight: 600;
    padding: 8px 20px;
    border-radius: 25px;
    margin-bottom: 15px;
    display: inline-block;
}

.bp-status.normal {
    background: #d5f4e6;
    color: #27ae60;
}

.bp-status.elevated {
    background: #fef9e7;
    color: #f39c12;
}

.bp-status.high {
    background: #fadbd8;
    color: #e74c3c;
}

.pulse {
    font-size: 1.1rem;
    color: #7f8c8d;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.pulse i {
    color: #e74c3c;
    animation: heartbeat 1.5s infinite;
}

/* Daily Insight Card */
.insight-card {
    grid-column: span 2;
}

.insight-content {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 20px;
}

.insight-message {
    font-size: 1.1rem;
    line-height: 1.6;
    color: #2c3e50;
}

.refresh-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.refresh-btn:hover {
    background: #5a6fd8;
    transform: rotate(180deg);
}

/* Quick Actions */
.quick-actions {
    grid-column: span 2;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.action-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 25px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.action-card:hover {
    transform: translateY(-5px);
    border-color: #667eea;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
}

.action-card i {
    font-size: 2.5rem;
    margin-bottom: 15px;
    color: #667eea;
}

.action-card h3 {
    font-size: 1.2rem;
    margin-bottom: 8px;
    color: #2c3e50;
}

.action-card p {
    color: #7f8c8d;
    font-size: 0.9rem;
}

/* Reminders */
.reminders-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.reminder-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.reminder-item:hover {
    background: #e9ecef;
}

.reminder-item i {
    font-size: 1.3rem;
    color: #667eea;
    width: 25px;
}

.reminder-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.reminder-title {
    font-weight: 600;
    color: #2c3e50;
}

.reminder-time {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.reminder-status {
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
}

.reminder-status.pending {
    background: #fef9e7;
    color: #f39c12;
}

/* Summary Stats */
.summary-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    text-align: center;
}

.stat-item {
    padding: 15px;
    background: #f8f9fa;
    border-radius: 12px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 5px;
}

.stat-number.good {
    color: #27ae60;
}

.stat-label {
    font-size: 0.9rem;
    color: #7f8c8d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Footer */
.footer {
    text-align: center;
    margin-top: 40px;
    padding: 20px;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
}

/* Animations */
@keyframes heartbeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        padding: 15px 20px;
    }
    
    .logo h1 {
        font-size: 1.5rem;
    }
    
    .bp-card,
    .insight-card,
    .quick-actions {
        grid-column: span 1;
    }
    
    .bp-numbers {
        font-size: 2.5rem;
    }
    
    .summary-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}
